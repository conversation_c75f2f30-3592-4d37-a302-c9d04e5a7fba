<script setup>
import { v4 as uuid } from 'uuid';
import { computed, ref } from 'vue';

const props = defineProps({
  modelValue: [String, Number, Boolean],
  options: {
    type: Object,
    required: true,
  },
  name: String,
  id: {
    type: String,
    default() {
      return `radio-input-${uuid()}`;
    },
  },
  error: String,
  label: String,
});

const searchQuery = ref('');

defineEmits(['update:modelValue']);

const newOptions = computed(() => {
  const options = [];
  if (props.options) {
    if (Array.isArray(props.options)) {
      return props.options;
    } else {
      Object.keys(props.options).forEach(key => {
        options.push({ label: props.options[key], value: key });
      });
    }
  }

  if (props.hasSearch && searchQuery.value) {
    const query = searchQuery.value.toLowerCase();
    return options.filter(
      option => option.label.toLowerCase().includes(query) || option.value.toLowerCase().includes(query),
    );
  }

  return options;
});
</script>

<template>
  <div>
    <label v-if="label" class="mb-2 block text-sm font-medium text-gray-700" :for="id">{{ label }}</label>
    <div class="flex items-center">
      <div v-for="option in newOptions" :key="option.value" class="mb-2 mr-4">
        <input
          :id="`${id}-${option.value}`"
          type="radio"
          :name="name"
          :value="option.value"
          :checked="modelValue == option.value"
          class="mr-2 form-radio rounded"
          @change="$emit('update:modelValue', option.value)"
        />
        <label :for="`${id}-${option.value}`">{{ option.label }}</label>
      </div>
    </div>
    <p v-if="error" class="text-theme-sm text-error-500">{{ error }}</p>
  </div>
</template>

<style scoped></style>
