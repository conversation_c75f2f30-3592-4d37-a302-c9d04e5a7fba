<?php

namespace Src\Domain\Admin\Requests\Faq;

use Illuminate\Foundation\Http\FormRequest;
use Src\Domain\Admin\Models\Faq\FaqForm;

class CreateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Rules
     *
     * @return array
     */
    public function rules(): array
    {
        return [
            'question' => [
                'required',
                'string',
                'max:255'
            ],
            'answer' => [
                'required',
                'string'
            ],
//            'is_public' => [
//                'boolean'
//            ]
        ];
    }

    /**
     * Attributes
     *
     * @return array
     */
    public function attributes(): array
    {
        return [
            'question' => __('models/faq.field.question'),
            'answer' => __('models/faq.field.answer'),
            'is_public' => __('models/faq.field.isPublic'),
        ];
    }

    /**
     * Get the validated form data.
     */
    public function validatedForm(): FaqForm
    {
        return new FaqForm($this->validated());
    }
}
