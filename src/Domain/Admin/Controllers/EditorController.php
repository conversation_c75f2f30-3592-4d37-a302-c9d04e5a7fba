<?php

namespace Src\Domain\Admin\Controllers;

use Illuminate\Http\JsonResponse;
use Src\Domain\Admin\Requests\Editor\UploadRequest;
use Src\Domain\Admin\Services\EditorService;
use Src\Enums\ResultCode;

class EditorController extends Controller
{
    /**
     * Upload image temporarily for CKEditor
     *
     * @param UploadRequest $request
     * @param EditorService $service
     * @return JsonResponse
     */
    public function upload(UploadRequest $request, EditorService $service): JsonResponse
    {
        try {
            $result = $service->uploadImageTemporary($request->validatedForm());

            if (!$result) {
                return response()->json([
                    'error' => [
                        'message' => __('flash.upload.failed')
                    ]
                ], 400);
            }

            // CKEditor SimpleUploadAdapter expects this exact format
            return response()->json([
                'url' => $result['url']
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'error' => [
                    'message' => $e->getMessage()
                ]
            ], 500);
        }
    }
}
