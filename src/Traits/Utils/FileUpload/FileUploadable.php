<?php

namespace Src\Traits\Utils\FileUpload;

use Illuminate\Http\UploadedFile;
use Src\Enums\FileDiv;
use Storage;
use App\Exceptions\ErrorException;

/**
 * Trait FileUploadable
 * @package SRc\Traits\Utils\FileUpload
 */
trait FileUploadable
{

    /**
     * Upload to tmp storage
     *
     * @param FileUploadInterface $upload
     * @param string $file_path
     * @return array|null
     */
    public function uploadTmpStorage(FileUploadInterface $upload, string $file_path = 'uploads/tmp'): ?array
    {
        $config_driver = config('filesystems.default');
        if( $config_driver && $config_driver != 's3'){
            $disk = 'public';
        }else{
            $disk = $config_driver;
        }

        $file = $upload->getFile();
        $result = null;
        try {
            $result = $this->putFileToTmpStorage($file, $file_path, $disk);
            if ($upload->getOldFilePath()) {
                $old_tmp_path = str_replace('/storage', '', $upload->getOldFilePath());
                if (Storage::disk($disk)->exists($old_tmp_path)) {
                    Storage::disk($disk)->delete($old_tmp_path);
                }

            }
            logger()->info('success to upload file to storage', ['name' => $result['fileName'], 'file_path' => $file_path, 'result' => true]);
        } catch (\Throwable $exception) {
            logger()->debug('Failed to put file to storage');
            logger()->error($exception);
        }

        return $result;
    }

    /**
     * Upload multiple file to tmp storage
     *
     * @param FileUploadInterface $upload
     * @param string $file_path
     * @return array|null
     */
    public function uploadMultipleTmpStorage(FileUploadInterface $upload, string $file_path = '/uploads/tmp'): ?array
    {
        $files = $upload->getFiles();
        $result = null;
        try {
            if (count($files) > 0) {
                $result = [];
                /** @var UploadedFile $file */
                foreach ($files as $file) {
                    $upload_result = $this->putFileToTmpStorage($file, $file_path);
                    $result[] = $upload_result;
                }

                logger()->info('success to upload file to storage', [
                    'files' => array_map(static function ($arr) use ($file_path) {
                        return ['name' => $arr['fileName'], 'file_path' => $file_path];
                    }, $result),
                    'result' => true,
                ]);
            }
        } catch (\Throwable $exception) {
            logger()->debug('Failed to put file to storage');
            logger()->error($exception);
        }

        return $result;
    }

    /**
     * moveFileToRegularPath
     *
     * @param string $tmp_path
     * @param string $dest
     * @return array
     * @throws ErrorException
     */
    private function moveFileToRegularPath(string $tmp_path, string $dest): array
    {
        $disk = config('filesystems.default');
        $arr = explode('/', $tmp_path);
        $file_name = $arr[count($arr) - 1];
        $regular_path = sprintf('/%s/%s', $dest, $file_name);
        $tmp_path = str_replace('/storage', '', $tmp_path);
        $move_result = Storage::disk($disk)->move($tmp_path, $regular_path);
        if (!$move_result) {
            throw new ErrorException('Failed to move file from tmp path to regular path', compact('tmp_path', 'regular_path'));
        }
        logger()->info('success to move file from tmp path to regular path', compact('tmp_path', 'regular_path'));
        return [
            'file_path' => "$dest/$file_name",
            'file_size' => Storage::disk($disk)->getSize($regular_path),
            'file_type' => Storage::disk($disk)->getMimetype($regular_path),
            'file_url' => env('APP_URL') . "/storage/$dest/$file_name",
        ];
    }

    /**
     * put File To Storage
     *
     * @param UploadedFile $file
     * @param string $file_path
     * @return array
     * @throws ErrorException
     */
    public function putFileToStorage(UploadedFile $file, string $file_path): array
    {
        $disk = config('filesystems.default');
        $file_extension = $file->getClientOriginalExtension();
        if(empty($file_extension)){
            $file_extension = 'jpg';
        }

        $file_name = sprintf('%s_%s.%s', str_random(10), now()->format('YmdHis'), $file_extension);
        $upload_result = Storage::disk($disk)->putFileAs($file_path, $file, $file_name);

        if (!$upload_result) {
            throw new ErrorException('Failed to put file to storage');
        }
        if ($disk == 's3') {
            $file_url = Storage::disk($disk)->url(sprintf('%s/%s', "$file_path", $file_name));
        } else {
            $file_url = env('APP_URL') . "/storage/$file_path/$file_name";
        }

        return [
            'file_path' => "$file_path/$file_name",
            'file_size' => $file->getSize(),
            'file_type' => $file->getMimeType(),
            'file_url' => $file_url,
            'file_div' => FileDiv::IMAGE
        ];
    }

    /**
     * put File To Storage from URL
     *
     * @param string $url
     * @param string $file_path
     * @return array
     * @throws ErrorException
     */
    public function putFileToStorageFromUrl(string $url, string $file_path): array
    {
        $disk = config('filesystems.default');
        $info = pathinfo($url);
        $contents = file_get_contents($url);

        $file = '/tmp/' . $info['basename'];
        file_put_contents($file, $contents);
        $uploaded_file = new UploadedFile($file, $info['basename']);

        return $this->putFileToStorage($uploaded_file, $file_path);
    }

    /**
     * put File To Tmp Storage
     *
     * @param UploadedFile $file
     * @param string $file_path
     * @return array
     * @throws ErrorException
     */
    private function putFileToTmpStorage(UploadedFile $file, string $file_path): array
    {
        $disk = 'public';
        $file_extension = $file->getClientOriginalExtension();
        $file_name = sprintf('%s_%s.%s', str_random(10), now()->format('YmdHis'), $file_extension);
        $upload_result = Storage::disk($disk)->putFileAs($file_path, $file, $file_name);
        $file_size = $file->getSize();

        if (!$upload_result) {
            throw new ErrorException('Failed to put file to storage');
        }

        return [
            'originFileName' => $file->getClientOriginalName(),
            'fileName' => $file_name,
            'filePath' => Storage::url(sprintf('%s/%s', $file_path, $file_name)),
            'fileSize' => $file_size
        ];
    }

    /**
     * Delete file from storage
     *
     * @param string $filePath
     * @return bool
     */
    public function deleteFileFromStorage(string $filePath): bool
    {
        $disk = config('filesystems.default');
        try {
            if (Storage::disk($disk)->exists($filePath)) {
                return Storage::disk($disk)->delete($filePath);
            }
            return true;
        } catch (\Throwable $e) {
            logger()->error('Failed to delete file from storage: ' . $e->getMessage());
            return false;
        }
    }
}
